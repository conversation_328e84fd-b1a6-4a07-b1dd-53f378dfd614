{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 11876527447619405325, "path": 8209745933529762091, "deps": [[2828590642173593838, "cfg_if", false, 17210828561417997836]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-efb0b30eb1558ac0\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}